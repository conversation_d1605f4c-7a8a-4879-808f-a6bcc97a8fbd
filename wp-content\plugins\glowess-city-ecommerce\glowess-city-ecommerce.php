<?php
/**
 * Plugin Name: Glowess City Based E-Commerce
 * Plugin URI: https://sewpos.com
 * Description: Şehir bazlı e-ticaret sistemi - Glowess teması ve WooCommerce uyumlu
 * Version: 1.0.0
 * Author: <PERSON><PERSON>ez
 * License: GPL v2 or later
 * Text Domain: glowess-city
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.8.2
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 9.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Plugin constants
if ( ! defined( 'GLOWESS_CITY_VERSION' ) ) {
    define( 'GLOWESS_CITY_VERSION', '1.0.0' );
}
if ( ! defined( 'GLOWESS_CITY_PLUGIN_URL' ) ) {
    define( 'GLOWESS_CITY_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
}
if ( ! defined( 'GLOWESS_CITY_PLUGIN_PATH' ) ) {
    define( 'GLOWESS_CITY_PLUGIN_PATH', plugin_dir_path( __FILE__ ) );
}
if ( ! defined( 'GLOWESS_CITY_PLUGIN_FILE' ) ) {
    define( 'GLOWESS_CITY_PLUGIN_FILE', __FILE__ );
}

/**
 * Main plugin class
 */
if ( ! class_exists( 'Glowess_City_Ecommerce' ) ) {
    
    class Glowess_City_Ecommerce {
        
        /**
         * Plugin instance
         */
        private static $instance = null;
        
        /**
         * Get plugin instance
         */
        public static function get_instance() {
            if ( null === self::$instance ) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        /**
         * Constructor
         */
        private function __construct() {
            add_action( 'init', array( $this, 'init' ) );
            add_action( 'plugins_loaded', array( $this, 'load_textdomain' ) );
            register_activation_hook( GLOWESS_CITY_PLUGIN_FILE, array( $this, 'activate' ) );
            register_deactivation_hook( GLOWESS_CITY_PLUGIN_FILE, array( $this, 'deactivate' ) );
        }
        
        /**
         * Initialize plugin
         */
        public function init() {
            // Check WooCommerce dependency
            if ( ! class_exists( 'WooCommerce' ) ) {
                add_action( 'admin_notices', array( $this, 'woocommerce_missing_notice' ) );
                return;
            }
            
            // Create custom post type
            $this->create_cities_post_type();
            
            // Initialize hooks
            $this->init_hooks();
            
            // Initialize admin functionality
            if ( is_admin() ) {
                $this->init_admin();
            }
        }
        
        /**
         * Initialize hooks
         */
        private function init_hooks() {
            // Meta boxes for cities post type
            add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
            add_action( 'save_post', array( $this, 'save_meta_boxes' ) );
            
            // AJAX handlers
            add_action( 'wp_ajax_glowess_select_city', array( $this, 'handle_city_selection' ) );
            add_action( 'wp_ajax_nopriv_glowess_select_city', array( $this, 'handle_city_selection' ) );
            
            // WooCommerce product hooks
            add_action( 'woocommerce_product_options_general_product_data', array( $this, 'add_city_fields_to_product' ) );
            add_action( 'woocommerce_process_product_meta', array( $this, 'save_product_city_fields' ) );

            // WooCommerce category hooks
            add_action( 'product_cat_add_form_fields', array( $this, 'add_city_fields_to_category_add' ) );
            add_action( 'product_cat_edit_form_fields', array( $this, 'add_city_fields_to_category_edit' ) );
            add_action( 'edited_product_cat', array( $this, 'save_category_city_fields' ) );
            add_action( 'create_product_cat', array( $this, 'save_category_city_fields' ) );

            // Query filtering
            add_action( 'pre_get_posts', array( $this, 'filter_products_by_city' ) );
            add_filter( 'get_terms_args', array( $this, 'filter_category_terms' ), 10, 2 );
            add_filter( 'woocommerce_product_query_meta_query', array( $this, 'filter_woocommerce_products' ), 10, 2 );
            
            // Frontend hooks
            add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
            add_action( 'wp_footer', array( $this, 'city_selector_modal' ) );
            
            // Shortcodes
            add_shortcode( 'glowess_city_selector', array( $this, 'city_selector_shortcode' ) );
        }
        
        /**
         * Initialize admin functionality
         */
        private function init_admin() {
            add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
            add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_scripts' ) );
        }
        
        /**
         * Create cities custom post type
         */
        public function create_cities_post_type() {
            $labels = array(
                'name'               => esc_html__( 'Şehirler', 'glowess-city' ),
                'singular_name'      => esc_html__( 'Şehir', 'glowess-city' ),
                'add_new'           => esc_html__( 'Yeni Şehir Ekle', 'glowess-city' ),
                'add_new_item'      => esc_html__( 'Yeni Şehir Ekle', 'glowess-city' ),
                'edit_item'         => esc_html__( 'Şehir Düzenle', 'glowess-city' ),
                'new_item'          => esc_html__( 'Yeni Şehir', 'glowess-city' ),
                'view_item'         => esc_html__( 'Şehri Görüntüle', 'glowess-city' ),
                'search_items'      => esc_html__( 'Şehir Ara', 'glowess-city' ),
                'not_found'         => esc_html__( 'Şehir bulunamadı', 'glowess-city' ),
                'not_found_in_trash' => esc_html__( 'Çöp kutusunda şehir bulunamadı', 'glowess-city' ),
            );
            
            $args = array(
                'labels'             => $labels,
                'public'             => true,
                'publicly_queryable' => false,
                'show_ui'            => true,
                'show_in_menu'       => true,
                'query_var'          => true,
                'rewrite'            => false,
                'capability_type'    => 'post',
                'has_archive'        => false,
                'hierarchical'       => false,
                'menu_position'      => 25,
                'menu_icon'          => 'dashicons-location-alt',
                'supports'           => array( 'title', 'editor', 'thumbnail' ),
            );
            
            register_post_type( 'cities', $args );
        }
        
        /**
         * Load textdomain
         */
        public function load_textdomain() {
            load_plugin_textdomain( 'glowess-city', false, dirname( plugin_basename( __FILE__ ) ) . '/languages/' );
        }
        
        /**
         * Activation hook
         */
        public function activate() {
            $this->create_cities_post_type();
            flush_rewrite_rules();
            
            // Default settings
            add_option( 'glowess_city_cart_threshold', 500 );
            add_option( 'glowess_city_discount_percentage', 10 );
        }
        
        /**
         * Deactivation hook
         */
        public function deactivate() {
            flush_rewrite_rules();
        }
        
        /**
         * WooCommerce missing notice
         */
        public function woocommerce_missing_notice() {
            ?>
            <div class="notice notice-error">
                <p><strong><?php esc_html_e( 'Glowess City Based E-Commerce:', 'glowess-city' ); ?></strong> 
                <?php esc_html_e( 'Bu eklenti çalışması için WooCommerce eklentisinin yüklü ve aktif olması gerekmektedir.', 'glowess-city' ); ?></p>
            </div>
            <?php
        }
        
        /**
         * Add meta boxes
         */
        public function add_meta_boxes() {
            add_meta_box(
                'glowess_city_details',
                esc_html__( 'Şehir Detayları', 'glowess-city' ),
                array( $this, 'city_details_meta_box' ),
                'cities',
                'normal',
                'high'
            );
        }

        /**
         * City details meta box
         */
        public function city_details_meta_box( $post ) {
            // Security nonce
            wp_nonce_field( 'glowess_city_details_nonce', 'glowess_city_details_nonce' );

            // Get current values
            $hero_image = get_post_meta( $post->ID, '_city_hero_image', true );
            $delivery_areas = get_post_meta( $post->ID, '_city_delivery_areas', true );
            $delivery_times = get_post_meta( $post->ID, '_city_delivery_times', true );
            $is_active = get_post_meta( $post->ID, '_city_is_active', true );
            $city_slug = get_post_meta( $post->ID, '_city_slug', true );
            ?>
            <table class="form-table">
                <tr>
                    <th><label for="city_hero_image"><?php esc_html_e( 'Hero Görseli URL', 'glowess-city' ); ?></label></th>
                    <td>
                        <input type="url" id="city_hero_image" name="city_hero_image" value="<?php echo esc_attr( $hero_image ); ?>" class="regular-text" />
                        <p class="description"><?php esc_html_e( 'Ana sayfa büyük görsel URL\'si', 'glowess-city' ); ?></p>
                    </td>
                </tr>
                <tr>
                    <th><label for="city_slug"><?php esc_html_e( 'Şehir Kodu', 'glowess-city' ); ?></label></th>
                    <td>
                        <input type="text" id="city_slug" name="city_slug" value="<?php echo esc_attr( $city_slug ); ?>" class="regular-text" />
                        <p class="description"><?php esc_html_e( 'URL için kullanılacak (örn: istanbul, ankara)', 'glowess-city' ); ?></p>
                    </td>
                </tr>
                <tr>
                    <th><label for="city_delivery_areas"><?php esc_html_e( 'Teslimat Alanları', 'glowess-city' ); ?></label></th>
                    <td>
                        <textarea id="city_delivery_areas" name="city_delivery_areas" rows="4" cols="50"><?php echo esc_textarea( $delivery_areas ); ?></textarea>
                        <p class="description"><?php esc_html_e( 'Her satıra bir ilçe yazın', 'glowess-city' ); ?></p>
                    </td>
                </tr>
                <tr>
                    <th><label for="city_delivery_times"><?php esc_html_e( 'Teslimat Süreleri', 'glowess-city' ); ?></label></th>
                    <td>
                        <textarea id="city_delivery_times" name="city_delivery_times" rows="4" cols="50"><?php echo esc_textarea( $delivery_times ); ?></textarea>
                        <p class="description"><?php esc_html_e( 'İlçe: Teslimat süresi formatında', 'glowess-city' ); ?></p>
                    </td>
                </tr>
                <tr>
                    <th><label for="city_is_active"><?php esc_html_e( 'Durum', 'glowess-city' ); ?></label></th>
                    <td>
                        <label>
                            <input type="checkbox" id="city_is_active" name="city_is_active" value="1" <?php checked( $is_active, '1' ); ?> />
                            <?php esc_html_e( 'Aktif', 'glowess-city' ); ?>
                        </label>
                    </td>
                </tr>
            </table>
            <?php
        }

        /**
         * Save meta boxes
         */
        public function save_meta_boxes( $post_id ) {
            // Verify nonce
            if ( ! isset( $_POST['glowess_city_details_nonce'] ) ||
                 ! wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['glowess_city_details_nonce'] ) ), 'glowess_city_details_nonce' ) ) {
                return;
            }

            // Check autosave
            if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
                return;
            }

            // Check user capabilities
            if ( ! current_user_can( 'edit_post', $post_id ) ) {
                return;
            }

            // Check post type
            if ( get_post_type( $post_id ) !== 'cities' ) {
                return;
            }

            // Save fields
            $fields = array(
                'city_hero_image'     => 'esc_url_raw',
                'city_slug'          => 'sanitize_title',
                'city_delivery_areas' => 'sanitize_textarea_field',
                'city_delivery_times' => 'sanitize_textarea_field',
                'city_is_active'     => 'absint'
            );

            foreach ( $fields as $field => $sanitize_callback ) {
                if ( isset( $_POST[ $field ] ) ) {
                    $value = call_user_func( $sanitize_callback, wp_unslash( $_POST[ $field ] ) );
                    update_post_meta( $post_id, '_' . $field, $value );
                } elseif ( $field === 'city_is_active' ) {
                    // Checkbox not checked
                    update_post_meta( $post_id, '_' . $field, 0 );
                }
            }
        }

        /**
         * Add city fields to product
         */
        public function add_city_fields_to_product() {
            global $post;

            if ( ! current_user_can( 'edit_post', $post->ID ) ) {
                return;
            }

            echo '<div class="product_city_fields show_if_simple show_if_variable">';

            // Get cities
            $cities = get_posts( array(
                'post_type'      => 'cities',
                'numberposts'    => -1,
                'post_status'    => 'publish',
                'orderby'        => 'title',
                'order'          => 'ASC'
            ) );

            $selected_cities = get_post_meta( $post->ID, '_available_cities', true );
            if ( ! is_array( $selected_cities ) ) {
                $selected_cities = array();
            }

            echo '<p class="form-field"><label><strong>' . esc_html__( 'Bu ürün hangi şehirlerde satılacak?', 'glowess-city' ) . '</strong></label></p>';

            if ( empty( $cities ) ) {
                echo '<p class="form-field">';
                echo '<em>' . esc_html__( 'Önce şehir eklemeniz gerekiyor.', 'glowess-city' ) . '</em>';
                echo '</p>';
            } else {
                foreach ( $cities as $city ) {
                    $checked = in_array( $city->ID, $selected_cities, true ) ? 'checked' : '';
                    echo '<p class="form-field">';
                    echo '<label><input type="checkbox" name="available_cities[]" value="' . esc_attr( $city->ID ) . '" ' . $checked . '> ' . esc_html( $city->post_title ) . '</label>';
                    echo '</p>';
                }
            }

            echo '</div>';
        }

        /**
         * Save product city fields
         */
        public function save_product_city_fields( $post_id ) {
            // Check user capabilities
            if ( ! current_user_can( 'edit_post', $post_id ) ) {
                return;
            }

            // Check post type
            if ( get_post_type( $post_id ) !== 'product' ) {
                return;
            }

            $available_cities = array();
            if ( isset( $_POST['available_cities'] ) && is_array( $_POST['available_cities'] ) ) {
                $available_cities = array_map( 'absint', $_POST['available_cities'] );
            }

            update_post_meta( $post_id, '_available_cities', $available_cities );
        }

        /**
         * Add city fields to category add form
         */
        public function add_city_fields_to_category_add() {
            $cities = get_posts( array(
                'post_type'   => 'cities',
                'numberposts' => -1,
                'post_status' => 'publish',
                'orderby'     => 'title',
                'order'       => 'ASC'
            ) );

            if ( empty( $cities ) ) {
                echo '<div class="form-field">';
                echo '<p><strong>' . esc_html__( 'Uyarı:', 'glowess-city' ) . '</strong> ' . esc_html__( 'Önce şehir eklemeniz gerekiyor.', 'glowess-city' ) . '</p>';
                echo '</div>';
                return;
            }

            echo '<div class="form-field">';
            echo '<label><strong>' . esc_html__( 'Bu kategori hangi şehirlerde gösterilecek?', 'glowess-city' ) . '</strong></label>';
            echo '<p class="description">' . esc_html__( 'Kategorinin hangi şehirlerde görüneceğini seçin. Hiçbiri seçilmezse tüm şehirlerde görünür.', 'glowess-city' ) . '</p>';

            foreach ( $cities as $city ) {
                echo '<p>';
                echo '<label><input type="checkbox" name="available_cities[]" value="' . esc_attr( $city->ID ) . '"> ' . esc_html( $city->post_title ) . '</label>';
                echo '</p>';
            }

            echo '</div>';
        }

        /**
         * Add city fields to category edit form
         */
        public function add_city_fields_to_category_edit( $term ) {
            $cities = get_posts( array(
                'post_type'   => 'cities',
                'numberposts' => -1,
                'post_status' => 'publish',
                'orderby'     => 'title',
                'order'       => 'ASC'
            ) );

            if ( empty( $cities ) ) {
                echo '<tr class="form-field">';
                echo '<th scope="row"><label><strong>' . esc_html__( 'Şehir Seçimi', 'glowess-city' ) . '</strong></label></th>';
                echo '<td><p><strong>' . esc_html__( 'Uyarı:', 'glowess-city' ) . '</strong> ' . esc_html__( 'Önce şehir eklemeniz gerekiyor.', 'glowess-city' ) . '</p></td>';
                echo '</tr>';
                return;
            }

            $selected_cities = get_term_meta( $term->term_id, '_available_cities', true );
            if ( ! is_array( $selected_cities ) ) {
                $selected_cities = array();
            }

            echo '<tr class="form-field">';
            echo '<th scope="row"><label><strong>' . esc_html__( 'Bu kategori hangi şehirlerde gösterilecek?', 'glowess-city' ) . '</strong></label></th>';
            echo '<td>';
            echo '<p class="description">' . esc_html__( 'Kategorinin hangi şehirlerde görüneceğini seçin. Hiçbiri seçilmezse tüm şehirlerde görünür.', 'glowess-city' ) . '</p>';

            foreach ( $cities as $city ) {
                $checked = in_array( $city->ID, $selected_cities, true ) ? 'checked' : '';
                echo '<p>';
                echo '<label><input type="checkbox" name="available_cities[]" value="' . esc_attr( $city->ID ) . '" ' . $checked . '> ' . esc_html( $city->post_title ) . '</label>';
                echo '</p>';
            }

            echo '</td>';
            echo '</tr>';
        }

        /**
         * Save category city fields
         */
        public function save_category_city_fields( $term_id ) {
            // Check user capabilities
            if ( ! current_user_can( 'manage_categories' ) ) {
                return;
            }

            $available_cities = array();
            if ( isset( $_POST['available_cities'] ) && is_array( $_POST['available_cities'] ) ) {
                $available_cities = array_map( 'absint', $_POST['available_cities'] );
            }

            update_term_meta( $term_id, '_available_cities', $available_cities );
        }

        /**
         * Filter products by city
         */
        public function filter_products_by_city( $query ) {
            if ( is_admin() || ! $query->is_main_query() ) {
                return;
            }

            $selected_city = $this->get_selected_city();
            if ( ! $selected_city || ! ( is_shop() || is_product_category() || is_home() ) ) {
                return;
            }

            $meta_query = $query->get( 'meta_query' );
            if ( ! is_array( $meta_query ) ) {
                $meta_query = array();
            }

            $meta_query[] = array(
                'key'     => '_available_cities',
                'value'   => serialize( strval( $selected_city ) ),
                'compare' => 'LIKE'
            );

            $query->set( 'meta_query', $meta_query );
        }

        /**
         * Filter category terms by city
         */
        public function filter_category_terms( $args, $taxonomies ) {
            if ( is_admin() || ! in_array( 'product_cat', $taxonomies, true ) ) {
                return $args;
            }

            $selected_city = $this->get_selected_city();
            if ( ! $selected_city ) {
                return $args;
            }

            if ( ! isset( $args['meta_query'] ) || ! is_array( $args['meta_query'] ) ) {
                $args['meta_query'] = array();
            }

            $args['meta_query'][] = array(
                'relation' => 'OR',
                array(
                    'key'     => '_available_cities',
                    'value'   => serialize( strval( $selected_city ) ),
                    'compare' => 'LIKE'
                ),
                array(
                    'key'     => '_available_cities',
                    'compare' => 'NOT EXISTS'
                ),
                array(
                    'key'     => '_available_cities',
                    'value'   => '',
                    'compare' => '='
                )
            );

            return $args;
        }

        /**
         * Filter WooCommerce products
         */
        public function filter_woocommerce_products( $meta_query, $query ) {
            if ( is_admin() ) {
                return $meta_query;
            }

            $selected_city = $this->get_selected_city();
            if ( ! $selected_city || ! isset( $query->query_vars['post_type'] ) || $query->query_vars['post_type'] !== 'product' ) {
                return $meta_query;
            }

            if ( ! is_array( $meta_query ) ) {
                $meta_query = array();
            }

            $meta_query[] = array(
                'key'     => '_available_cities',
                'value'   => serialize( strval( $selected_city ) ),
                'compare' => 'LIKE'
            );

            return $meta_query;
        }

        /**
         * Get selected city ID
         */
        public function get_selected_city() {
            if ( isset( $_COOKIE['selected_city_id'] ) ) {
                return absint( $_COOKIE['selected_city_id'] );
            }
            return false;
        }

        /**
         * Get selected city slug
         */
        public function get_selected_city_slug() {
            if ( isset( $_COOKIE['selected_city_slug'] ) ) {
                return sanitize_text_field( $_COOKIE['selected_city_slug'] );
            }
            return false;
        }

        /**
         * Handle AJAX city selection
         */
        public function handle_city_selection() {
            // Verify nonce
            if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['nonce'] ) ), 'glowess_city_nonce' ) ) {
                wp_send_json_error( esc_html__( 'Güvenlik kontrolü başarısız.', 'glowess-city' ) );
            }

            $city_id = isset( $_POST['city_id'] ) ? absint( $_POST['city_id'] ) : 0;

            if ( $city_id && get_post_type( $city_id ) === 'cities' ) {
                $city_slug = get_post_meta( $city_id, '_city_slug', true );
                if ( ! $city_slug ) {
                    $city_slug = sanitize_title( get_the_title( $city_id ) );
                }

                wp_send_json_success( array(
                    'city_id'   => $city_id,
                    'city_name' => get_the_title( $city_id ),
                    'city_slug' => $city_slug
                ) );
            } else {
                wp_send_json_error( esc_html__( 'Geçersiz şehir.', 'glowess-city' ) );
            }
        }

        /**
         * Enqueue scripts and styles
         */
        public function enqueue_scripts() {
            wp_enqueue_script(
                'glowess-city-js',
                GLOWESS_CITY_PLUGIN_URL . 'assets/script.js',
                array( 'jquery' ),
                GLOWESS_CITY_VERSION,
                true
            );

            wp_enqueue_style(
                'glowess-city-css',
                GLOWESS_CITY_PLUGIN_URL . 'assets/style.css',
                array(),
                GLOWESS_CITY_VERSION
            );

            wp_localize_script( 'glowess-city-js', 'glowess_city_ajax', array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'glowess_city_nonce' )
            ) );
        }

        /**
         * City selector modal
         */
        public function city_selector_modal() {
            $selected_city = $this->get_selected_city();

            if ( ! $selected_city ) {
                $cities = get_posts( array(
                    'post_type'   => 'cities',
                    'numberposts' => -1,
                    'post_status' => 'publish',
                    'meta_query'  => array(
                        array(
                            'key'     => '_city_is_active',
                            'value'   => '1',
                            'compare' => '='
                        )
                    ),
                    'orderby' => 'title',
                    'order'   => 'ASC'
                ) );

                if ( ! empty( $cities ) ) {
                    ?>
                    <div id="city-selector-modal" class="city-modal-overlay">
                        <div class="city-modal-content">
                            <h3><?php esc_html_e( 'Şehrinizi Seçin', 'glowess-city' ); ?></h3>
                            <div class="city-options">
                                <?php foreach ( $cities as $city ) : ?>
                                    <div class="city-option" data-city-id="<?php echo esc_attr( $city->ID ); ?>">
                                        <?php echo esc_html( $city->post_title ); ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            }
        }

        /**
         * City selector shortcode
         */
        public function city_selector_shortcode( $atts ) {
            $atts = shortcode_atts( array(
                'style' => 'dropdown'
            ), $atts );

            $cities = get_posts( array(
                'post_type'   => 'cities',
                'numberposts' => -1,
                'post_status' => 'publish',
                'meta_query'  => array(
                    array(
                        'key'     => '_city_is_active',
                        'value'   => '1',
                        'compare' => '='
                    )
                ),
                'orderby' => 'title',
                'order'   => 'ASC'
            ) );

            if ( empty( $cities ) ) {
                return '<p>' . esc_html__( 'Henüz aktif şehir bulunmuyor.', 'glowess-city' ) . '</p>';
            }

            $selected_city = $this->get_selected_city();
            $selected_city_name = $selected_city ? get_the_title( $selected_city ) : esc_html__( 'Şehir Seçin', 'glowess-city' );

            ob_start();
            ?>
            <div class="glowess-city-selector">
                <div class="city-selector-dropdown">
                    <button class="city-selector-trigger"><?php echo esc_html( $selected_city_name ); ?></button>
                    <div class="city-options">
                        <?php foreach ( $cities as $city ) : ?>
                            <div class="city-option" data-city-id="<?php echo esc_attr( $city->ID ); ?>">
                                <?php echo esc_html( $city->post_title ); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php
            return ob_get_clean();
        }

        /**
         * Add admin menu
         */
        public function add_admin_menu() {
            add_menu_page(
                esc_html__( 'Glowess Şehir Ayarları', 'glowess-city' ),
                esc_html__( 'Şehir Ayarları', 'glowess-city' ),
                'manage_options',
                'glowess-city-settings',
                array( $this, 'admin_page' ),
                'dashicons-location-alt',
                30
            );
        }

        /**
         * Admin page
         */
        public function admin_page() {
            if ( isset( $_POST['submit'] ) && wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['_wpnonce'] ) ), 'glowess_city_settings' ) ) {
                update_option( 'glowess_city_cart_threshold', absint( $_POST['cart_threshold'] ) );
                update_option( 'glowess_city_discount_percentage', absint( $_POST['discount_percentage'] ) );
                echo '<div class="notice notice-success"><p>' . esc_html__( 'Ayarlar kaydedildi.', 'glowess-city' ) . '</p></div>';
            }

            $cart_threshold = get_option( 'glowess_city_cart_threshold', 500 );
            $discount_percentage = get_option( 'glowess_city_discount_percentage', 10 );
            ?>
            <div class="wrap">
                <h1><?php esc_html_e( 'Glowess Şehir Ayarları', 'glowess-city' ); ?></h1>

                <form method="post" action="">
                    <?php wp_nonce_field( 'glowess_city_settings' ); ?>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php esc_html_e( 'Sepet Eşik Tutarı', 'glowess-city' ); ?></th>
                            <td>
                                <input type="number" name="cart_threshold" value="<?php echo esc_attr( $cart_threshold ); ?>" min="0" step="1" />
                                <p class="description"><?php esc_html_e( 'Bu tutarın üzerindeki siparişlerde indirim uygulanır.', 'glowess-city' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php esc_html_e( 'İndirim Yüzdesi', 'glowess-city' ); ?></th>
                            <td>
                                <input type="number" name="discount_percentage" value="<?php echo esc_attr( $discount_percentage ); ?>" min="0" max="100" step="1" />
                                <p class="description"><?php esc_html_e( 'Eşik tutarı aşıldığında uygulanacak indirim yüzdesi.', 'glowess-city' ); ?></p>
                            </td>
                        </tr>
                    </table>
                    <?php submit_button(); ?>
                </form>
            </div>
            <?php
        }

        /**
         * Admin enqueue scripts
         */
        public function admin_enqueue_scripts( $hook ) {
            if ( 'toplevel_page_glowess-city-settings' === $hook ) {
                wp_enqueue_style(
                    'glowess-city-admin-css',
                    GLOWESS_CITY_PLUGIN_URL . 'assets/admin.css',
                    array(),
                    GLOWESS_CITY_VERSION
                );
            }
        }
    }
}

// Initialize plugin
if ( ! function_exists( 'glowess_city_ecommerce_init' ) ) {
    function glowess_city_ecommerce_init() {
        return Glowess_City_Ecommerce::get_instance();
    }
    add_action( 'plugins_loaded', 'glowess_city_ecommerce_init' );
}
